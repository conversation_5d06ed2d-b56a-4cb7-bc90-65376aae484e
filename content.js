// Function to add the AI Reply button
function addAiButton() {
    const toolbars = document.querySelectorAll('.gA.gt');
    toolbars.forEach(toolbar => {
        if (!toolbar.querySelector('#ai-reply-button')) {
            const aiButton = createAiButton();
            toolbar.appendChild(aiButton);
        }
    });
}

// Function to create the AI button with proper styling
function createAiButton() {
    const aiButton = document.createElement('button');
    aiButton.id = 'ai-reply-button';
    aiButton.innerHTML = '✨ AI Reply';

    // Add professional styling
    aiButton.style.cssText = `
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        margin: 0 4px;
        font-size: 13px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: inline-flex;
        align-items: center;
        gap: 4px;
        z-index: 1000;
        position: relative;
    `;

    // Add hover effects
    aiButton.addEventListener('mouseenter', () => {
        aiButton.style.transform = 'translateY(-1px)';
        aiButton.style.boxShadow = '0 4px 8px rgba(102, 126, 234, 0.3)';
    });

    aiButton.addEventListener('mouseleave', () => {
        aiButton.style.transform = 'translateY(0)';
        aiButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    });

    aiButton.addEventListener('click', showAiOptions);
    return aiButton;
}

// Function to show AI options dropdown
function showAiOptions(event) {
    event.preventDefault();
    event.stopPropagation();

    // Remove existing dropdown if any
    const existingDropdown = document.querySelector('#ai-options-dropdown');
    if (existingDropdown) {
        existingDropdown.remove();
        return;
    }

    const dropdown = createAiOptionsDropdown();
    const button = event.target.closest('#ai-reply-button');

    // Position dropdown below the button
    const rect = button.getBoundingClientRect();
    dropdown.style.position = 'fixed';
    dropdown.style.top = (rect.bottom + 5) + 'px';
    dropdown.style.left = rect.left + 'px';
    dropdown.style.zIndex = '10000';

    document.body.appendChild(dropdown);

    // Close dropdown when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closeDropdown(e) {
            if (!dropdown.contains(e.target) && !button.contains(e.target)) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        });
    }, 100);
}

// Function to create AI options dropdown
function createAiOptionsDropdown() {
    const dropdown = document.createElement('div');
    dropdown.id = 'ai-options-dropdown';
    dropdown.style.cssText = `
        background: white;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        border: 1px solid #e2e8f0;
        min-width: 250px;
        overflow: hidden;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    const options = [
        {
            icon: '📧',
            title: 'Smart Reply',
            description: 'Generate a contextual reply to this email',
            action: () => generateSmartReply()
        },
        {
            icon: '✍️',
            title: 'Improve Draft',
            description: 'Enhance your current draft',
            action: () => improveDraft()
        },
        {
            icon: '📝',
            title: 'Summarize Email',
            description: 'Create a summary of the email thread',
            action: () => summarizeEmail()
        },
        {
            icon: '🎯',
            title: 'Quick Response',
            description: 'Generate quick professional responses',
            action: () => showQuickResponses()
        }
    ];

    options.forEach(option => {
        const optionElement = document.createElement('div');
        optionElement.style.cssText = `
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        `;

        optionElement.innerHTML = `
            <span style="font-size: 18px;">${option.icon}</span>
            <div>
                <div style="font-weight: 600; color: #2d3748; font-size: 14px;">${option.title}</div>
                <div style="color: #718096; font-size: 12px; margin-top: 2px;">${option.description}</div>
            </div>
        `;

        optionElement.addEventListener('mouseenter', () => {
            optionElement.style.backgroundColor = '#f7fafc';
        });

        optionElement.addEventListener('mouseleave', () => {
            optionElement.style.backgroundColor = 'white';
        });

        optionElement.addEventListener('click', () => {
            dropdown.remove();
            option.action();
        });

        dropdown.appendChild(optionElement);
    });

    return dropdown;
}

// Utility function to get API key
async function getApiKey() {
    const storage = await chrome.storage.sync.get('geminiApiKey');
    return storage.geminiApiKey;
}

// Utility function to show loading state
function showLoadingState(message = 'Generating...') {
    const button = document.getElementById('ai-reply-button');
    if (button) {
        button.innerHTML = `🧠 ${message}`;
        button.disabled = true;
    }
}

// Utility function to reset button state
function resetButtonState() {
    const button = document.getElementById('ai-reply-button');
    if (button) {
        button.innerHTML = '✨ AI Reply';
        button.disabled = false;
    }
}

// Utility function to make API call
async function callGeminiAPI(prompt) {
    const apiKey = await getApiKey();

    if (!apiKey) {
        alert("Gemini API Key not found. Please set it in the extension options.");
        chrome.runtime.sendMessage({ action: "openOptionsPage" });
        throw new Error('No API key found');
    }

    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`;

    const payload = {
        contents: [{
            parts: [{ text: prompt }]
        }]
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
    });

    if (!response.ok) {
        throw new Error(`API Error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
}

// Function to get email context (both received email and current draft)
function getEmailContext() {
    // Try to find the compose area
    const composeArea = document.querySelector('.Am.Al.editable.LW-avf') ||
                       document.querySelector('[contenteditable="true"]') ||
                       document.querySelector('.editable');

    // Try to find the original email content (for replies)
    const originalEmail = document.querySelector('.ii.gt .a3s.aiL') ||
                         document.querySelector('.gmail_quote') ||
                         document.querySelector('.moz-cite-prefix');

    return {
        composeArea,
        currentDraft: composeArea ? composeArea.innerText.trim() : '',
        originalEmail: originalEmail ? originalEmail.innerText.trim() : '',
        hasOriginalEmail: !!originalEmail
    };
}

// Function to generate smart reply based on context
async function generateSmartReply() {
    try {
        showLoadingState('Analyzing email...');

        const context = getEmailContext();

        if (!context.composeArea) {
            alert("Please open a compose window or reply to an email first.");
            resetButtonState();
            return;
        }

        let prompt;
        if (context.hasOriginalEmail && context.originalEmail) {
            // Replying to an email
            prompt = `You are helping someone reply to an email. Please write a professional and appropriate reply to the following email:

ORIGINAL EMAIL:
${context.originalEmail}

${context.currentDraft ? `CURRENT DRAFT (to improve/continue):
${context.currentDraft}

Please improve or complete this draft.` : 'Please write a complete professional reply.'}

Requirements:
- Be professional and courteous
- Address the main points from the original email
- Keep it concise but complete
- Match the tone of the original email`;
        } else {
            // Composing a new email or improving draft
            if (!context.currentDraft || context.currentDraft.length < 10) {
                alert("Please provide some context or draft content first.");
                resetButtonState();
                return;
            }

            prompt = `Please help improve this email draft to make it more professional and effective:

CURRENT DRAFT:
${context.currentDraft}

Please rewrite it to be:
- More professional and polished
- Clear and concise
- Properly structured
- Appropriate for business communication`;
        }

        const aiReply = await callGeminiAPI(prompt);

        // Replace the content in the compose area
        context.composeArea.innerHTML = aiReply.replace(/\n/g, '<br>');

        // Trigger input event to ensure Gmail recognizes the change
        context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));

    } catch (error) {
        console.error('Smart Reply Error:', error);
        alert("Could not generate smart reply. Please check your API key and try again.");
    } finally {
        resetButtonState();
    }
}

// Function to improve current draft
async function improveDraft() {
    try {
        showLoadingState('Improving draft...');

        const context = getEmailContext();

        if (!context.composeArea || !context.currentDraft || context.currentDraft.length < 10) {
            alert("Please write some content in the email first, then I can help improve it.");
            resetButtonState();
            return;
        }

        const prompt = `Please improve this email draft to make it more professional, clear, and effective:

CURRENT DRAFT:
${context.currentDraft}

Please enhance it by:
- Improving clarity and readability
- Making it more professional
- Fixing any grammar or style issues
- Ensuring proper email structure
- Keeping the same general meaning and intent`;

        const improvedDraft = await callGeminiAPI(prompt);

        // Replace the content
        context.composeArea.innerHTML = improvedDraft.replace(/\n/g, '<br>');
        context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));

    } catch (error) {
        console.error('Improve Draft Error:', error);
        alert("Could not improve draft. Please check your API key and try again.");
    } finally {
        resetButtonState();
    }
}

// Function to summarize email thread
async function summarizeEmail() {
    try {
        showLoadingState('Summarizing...');

        const context = getEmailContext();

        if (!context.originalEmail) {
            alert("No email content found to summarize. Please open an email thread first.");
            resetButtonState();
            return;
        }

        const prompt = `Please create a concise summary of this email:

EMAIL CONTENT:
${context.originalEmail}

Please provide:
- Key points and main topics
- Any action items or requests
- Important dates or deadlines mentioned
- Overall purpose/intent of the email

Keep the summary brief but comprehensive.`;

        const summary = await callGeminiAPI(prompt);

        // Insert summary at the beginning of compose area
        const summaryText = `📋 EMAIL SUMMARY:\n${summary}\n\n---\n\n`;

        if (context.composeArea) {
            const currentContent = context.currentDraft;
            context.composeArea.innerHTML = (summaryText + currentContent).replace(/\n/g, '<br>');
            context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));
        } else {
            // Show summary in a popup if no compose area
            alert(`EMAIL SUMMARY:\n\n${summary}`);
        }

    } catch (error) {
        console.error('Summarize Error:', error);
        alert("Could not summarize email. Please check your API key and try again.");
    } finally {
        resetButtonState();
    }
}

// Function to show quick response options
function showQuickResponses() {
    const context = getEmailContext();

    if (!context.composeArea) {
        alert("Please open a compose window first.");
        return;
    }

    // Remove existing quick response popup if any
    const existingPopup = document.querySelector('#quick-responses-popup');
    if (existingPopup) {
        existingPopup.remove();
        return;
    }

    const popup = document.createElement('div');
    popup.id = 'quick-responses-popup';
    popup.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        border: 1px solid #e2e8f0;
        width: 400px;
        max-height: 500px;
        overflow-y: auto;
        z-index: 10000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    const quickResponses = [
        { title: 'Thank you', text: 'Thank you for your email. I appreciate you reaching out.' },
        { title: 'Acknowledge receipt', text: 'Thank you for your email. I have received your message and will review it shortly.' },
        { title: 'Schedule meeting', text: 'Thank you for your email. I would be happy to discuss this further. Could we schedule a meeting to talk about this in more detail?' },
        { title: 'Request more info', text: 'Thank you for reaching out. Could you please provide more details about [specific topic] so I can better assist you?' },
        { title: 'Positive response', text: 'Thank you for your email. This sounds great! I am interested and would like to move forward.' },
        { title: 'Polite decline', text: 'Thank you for thinking of me. Unfortunately, I won\'t be able to participate at this time, but I appreciate the opportunity.' },
        { title: 'Follow up', text: 'I wanted to follow up on my previous email. Please let me know if you need any additional information.' },
        { title: 'Apology', text: 'I apologize for the delay in responding. Thank you for your patience.' }
    ];

    popup.innerHTML = `
        <div style="padding: 20px; border-bottom: 1px solid #f1f5f9;">
            <h3 style="margin: 0; color: #2d3748; font-size: 18px;">Quick Responses</h3>
            <p style="margin: 8px 0 0 0; color: #718096; font-size: 14px;">Choose a template to get started</p>
        </div>
        <div style="padding: 12px;">
            ${quickResponses.map(response => `
                <div class="quick-response-item" style="
                    padding: 12px;
                    margin: 8px 0;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                " data-text="${response.text}">
                    <div style="font-weight: 600; color: #2d3748; margin-bottom: 4px;">${response.title}</div>
                    <div style="color: #718096; font-size: 13px;">${response.text}</div>
                </div>
            `).join('')}
        </div>
        <div style="padding: 16px; border-top: 1px solid #f1f5f9; text-align: right;">
            <button id="close-quick-responses" style="
                background: #f7fafc;
                color: #4a5568;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 16px;
                cursor: pointer;
                font-size: 14px;
            ">Close</button>
        </div>
    `;

    // Add hover effects and click handlers
    popup.addEventListener('click', (e) => {
        const item = e.target.closest('.quick-response-item');
        if (item) {
            const text = item.getAttribute('data-text');
            context.composeArea.innerHTML = text.replace(/\n/g, '<br>');
            context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));
            popup.remove();
        }

        if (e.target.id === 'close-quick-responses') {
            popup.remove();
        }
    });

    // Add hover effects
    popup.addEventListener('mouseover', (e) => {
        const item = e.target.closest('.quick-response-item');
        if (item) {
            item.style.backgroundColor = '#f7fafc';
            item.style.borderColor = '#cbd5e0';
        }
    });

    popup.addEventListener('mouseout', (e) => {
        const item = e.target.closest('.quick-response-item');
        if (item) {
            item.style.backgroundColor = 'white';
            item.style.borderColor = '#e2e8f0';
        }
    });

    document.body.appendChild(popup);

    // Close popup when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closePopup(e) {
            if (!popup.contains(e.target)) {
                popup.remove();
                document.removeEventListener('click', closePopup);
            }
        });
    }, 100);
}

// Enhanced observer to add the button when compose windows open
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        // Check for compose windows and reply areas
        if (mutation.addedNodes.length) {
            // Gmail compose toolbar
            const composeToolbars = document.querySelectorAll('.gA.gt');
            composeToolbars.forEach(toolbar => {
                if (!toolbar.querySelector('#ai-reply-button')) {
                    const aiButton = createAiButton();
                    toolbar.appendChild(aiButton);
                }
            });

            // Also check for reply toolbars
            const replyToolbars = document.querySelectorAll('.btC');
            replyToolbars.forEach(toolbar => {
                if (!toolbar.querySelector('#ai-reply-button')) {
                    const aiButton = createAiButton();
                    toolbar.appendChild(aiButton);
                }
            });
        }
    });
});

// Start observing
observer.observe(document.body, { childList: true, subtree: true });

// Also add button to any existing compose windows
setTimeout(() => {
    addAiButton();
}, 1000);