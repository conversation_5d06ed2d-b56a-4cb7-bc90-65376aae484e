// Function to add the AI Reply button
function addAiButton() {
    const toolbars = document.querySelectorAll('.gA.gt');
    toolbars.forEach(toolbar => {
        if (!toolbar.querySelector('#ai-reply-button')) {
            const aiButton = createAiButton();
            toolbar.appendChild(aiButton);
        }
    });
}

// Function to create the AI button with proper styling
function createAiButton() {
    const aiButton = document.createElement('button');
    aiButton.id = 'ai-reply-button';
    aiButton.innerHTML = '✨ AI Reply';

    // Add professional styling
    aiButton.style.cssText = `
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        margin: 0 4px;
        font-size: 13px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: inline-flex;
        align-items: center;
        gap: 4px;
        z-index: 1000;
        position: relative;
    `;

    // Add hover effects
    aiButton.addEventListener('mouseenter', () => {
        aiButton.style.transform = 'translateY(-1px)';
        aiButton.style.boxShadow = '0 4px 8px rgba(102, 126, 234, 0.3)';
    });

    aiButton.addEventListener('mouseleave', () => {
        aiButton.style.transform = 'translateY(0)';
        aiButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
    });

    aiButton.addEventListener('click', showAiOptions);
    return aiButton;
}

// Function to show AI options dropdown
function showAiOptions(event) {
    event.preventDefault();
    event.stopPropagation();

    // Remove existing dropdown if any
    const existingDropdown = document.querySelector('#ai-options-dropdown');
    if (existingDropdown) {
        existingDropdown.remove();
        return;
    }

    const dropdown = createAiOptionsDropdown();
    const button = event.target.closest('#ai-reply-button');

    // Add dropdown to body first to measure its height
    dropdown.style.position = 'fixed';
    dropdown.style.visibility = 'hidden';
    dropdown.style.zIndex = '10000';
    document.body.appendChild(dropdown);

    // Get button and dropdown dimensions
    const rect = button.getBoundingClientRect();
    const dropdownHeight = dropdown.offsetHeight;
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;

    // Determine if dropdown should appear above or below
    const shouldAppearAbove = spaceBelow < dropdownHeight && spaceAbove > dropdownHeight;

    // Position dropdown
    if (shouldAppearAbove) {
        // Position above the button
        dropdown.style.top = (rect.top - dropdownHeight - 5) + 'px';
        dropdown.style.transformOrigin = 'bottom';
        dropdown.style.animation = 'slideUpFadeIn 0.2s ease-out';
    } else {
        // Position below the button (default)
        dropdown.style.top = (rect.bottom + 5) + 'px';
        dropdown.style.transformOrigin = 'top';
        dropdown.style.animation = 'slideDownFadeIn 0.2s ease-out';
    }

    // Ensure dropdown doesn't go off-screen horizontally
    let leftPosition = rect.left;
    const dropdownWidth = dropdown.offsetWidth;
    const viewportWidth = window.innerWidth;

    if (leftPosition + dropdownWidth > viewportWidth) {
        leftPosition = viewportWidth - dropdownWidth - 10;
    }
    if (leftPosition < 10) {
        leftPosition = 10;
    }

    dropdown.style.left = leftPosition + 'px';
    dropdown.style.visibility = 'visible';

    // Add CSS animations if not already added
    if (!document.querySelector('#ai-dropdown-animations')) {
        const style = document.createElement('style');
        style.id = 'ai-dropdown-animations';
        style.textContent = `
            @keyframes slideDownFadeIn {
                from {
                    opacity: 0;
                    transform: translateY(-10px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            @keyframes slideUpFadeIn {
                from {
                    opacity: 0;
                    transform: translateY(10px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Close dropdown when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closeDropdown(e) {
            if (!dropdown.contains(e.target) && !button.contains(e.target)) {
                dropdown.style.animation = 'slideDownFadeIn 0.15s ease-in reverse';
                setTimeout(() => {
                    dropdown.remove();
                }, 150);
                document.removeEventListener('click', closeDropdown);
            }
        });
    }, 100);
}

// Function to create AI options dropdown
function createAiOptionsDropdown() {
    const dropdown = document.createElement('div');
    dropdown.id = 'ai-options-dropdown';
    dropdown.style.cssText = `
        background: white;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        border: 1px solid #e2e8f0;
        min-width: 280px;
        overflow: hidden;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    const options = [
        {
            icon: '📧',
            title: 'Smart Reply',
            description: 'Auto-generate contextual reply',
            action: () => showLengthOptions('reply')
        },
        {
            icon: '✍️',
            title: 'Improve Draft',
            description: 'Enhance your current writing',
            action: () => showLengthOptions('improve')
        },
        {
            icon: '📝',
            title: 'Summarize Email',
            description: 'Create a summary of the email thread',
            action: () => summarizeEmail()
        },
        {
            icon: '🎯',
            title: 'Quick Response',
            description: 'Use professional templates',
            action: () => showQuickResponses()
        }
    ];

    options.forEach(option => {
        const optionElement = document.createElement('div');
        optionElement.style.cssText = `
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        `;

        optionElement.innerHTML = `
            <span style="font-size: 18px;">${option.icon}</span>
            <div>
                <div style="font-weight: 600; color: #2d3748; font-size: 14px;">${option.title}</div>
                <div style="color: #718096; font-size: 12px; margin-top: 2px;">${option.description}</div>
            </div>
        `;

        optionElement.addEventListener('mouseenter', () => {
            optionElement.style.backgroundColor = '#f7fafc';
        });

        optionElement.addEventListener('mouseleave', () => {
            optionElement.style.backgroundColor = 'white';
        });

        optionElement.addEventListener('click', () => {
            dropdown.remove();
            option.action();
        });

        dropdown.appendChild(optionElement);
    });

    return dropdown;
}

// Function to show length options for Smart Reply and Improve Draft
function showLengthOptions(type) {
    // Remove existing dropdown if any
    const existingDropdown = document.querySelector('#length-options-dropdown');
    if (existingDropdown) {
        existingDropdown.remove();
        return;
    }

    const dropdown = document.createElement('div');
    dropdown.id = 'length-options-dropdown';
    dropdown.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        border: 1px solid #e2e8f0;
        width: 320px;
        z-index: 10000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        animation: slideDownFadeIn 0.2s ease-out;
    `;

    const title = type === 'reply' ? 'Smart Reply Options' : 'Improve Draft Options';
    const lengthOptions = [
        {
            icon: '⚡',
            title: 'Short & Concise',
            description: 'Brief, to-the-point response',
            length: 'short'
        },
        {
            icon: '📄',
            title: 'Medium Length',
            description: 'Balanced, professional response',
            length: 'medium'
        },
        {
            icon: '📋',
            title: 'Detailed & Complete',
            description: 'Comprehensive, thorough response',
            length: 'long'
        }
    ];

    dropdown.innerHTML = `
        <div style="padding: 20px; border-bottom: 1px solid #f1f5f9;">
            <h3 style="margin: 0; color: #2d3748; font-size: 18px;">${title}</h3>
            <p style="margin: 8px 0 0 0; color: #718096; font-size: 14px;">Choose your preferred response length</p>
        </div>
        <div style="padding: 12px;">
            ${lengthOptions.map(option => `
                <div class="length-option" style="
                    padding: 12px;
                    margin: 8px 0;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                " data-length="${option.length}" data-type="${type}">
                    <span style="font-size: 18px;">${option.icon}</span>
                    <div>
                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 4px;">${option.title}</div>
                        <div style="color: #718096; font-size: 13px;">${option.description}</div>
                    </div>
                </div>
            `).join('')}
        </div>
        <div style="padding: 16px; border-top: 1px solid #f1f5f9; text-align: right;">
            <button id="close-length-options" style="
                background: #f7fafc;
                color: #4a5568;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 16px;
                cursor: pointer;
                font-size: 14px;
            ">Cancel</button>
        </div>
    `;

    // Add event listeners
    dropdown.addEventListener('click', (e) => {
        const option = e.target.closest('.length-option');
        if (option) {
            const length = option.getAttribute('data-length');
            const actionType = option.getAttribute('data-type');
            dropdown.remove();

            if (actionType === 'reply') {
                generateSmartReply(length);
            } else {
                improveDraft(length);
            }
        }

        if (e.target.id === 'close-length-options') {
            dropdown.remove();
        }
    });

    // Add hover effects
    dropdown.addEventListener('mouseover', (e) => {
        const option = e.target.closest('.length-option');
        if (option) {
            option.style.backgroundColor = '#f7fafc';
            option.style.borderColor = '#cbd5e0';
        }
    });

    dropdown.addEventListener('mouseout', (e) => {
        const option = e.target.closest('.length-option');
        if (option) {
            option.style.backgroundColor = 'white';
            option.style.borderColor = '#e2e8f0';
        }
    });

    document.body.appendChild(dropdown);

    // Close dropdown when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closeDropdown(e) {
            if (!dropdown.contains(e.target)) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        });
    }, 100);
}

// Utility function to get API key, model, and custom prompt
async function getApiSettings() {
    const storage = await chrome.storage.sync.get(['geminiApiKey', 'geminiModel', 'customPrompt']);
    return {
        apiKey: storage.geminiApiKey,
        model: storage.geminiModel || 'gemini-2.5-flash', // Default to latest Flash model
        customPrompt: storage.customPrompt || '' // Custom user instructions
    };
}

// Utility function to show loading state
function showLoadingState(message = 'Generating...') {
    const button = document.getElementById('ai-reply-button');
    if (button) {
        button.innerHTML = `🧠 ${message}`;
        button.disabled = true;
    }
}

// Utility function to reset button state
function resetButtonState() {
    const button = document.getElementById('ai-reply-button');
    if (button) {
        button.innerHTML = '✨ AI Reply';
        button.disabled = false;
    }
}

// Utility function to make API call
async function callGeminiAPI(prompt) {
    const settings = await getApiSettings();

    if (!settings.apiKey) {
        alert("Gemini API Key not found. Please set it in the extension options.");
        chrome.runtime.sendMessage({ action: "openOptionsPage" });
        throw new Error('No API key found');
    }

    // Dynamic API URL based on selected model
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${settings.model}:generateContent`;

    const payload = {
        contents: [{
            parts: [{ text: prompt }]
        }]
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-goog-api-key': settings.apiKey  // Use the API key from settings
        },
        body: JSON.stringify(payload)
    });

    if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Details:', errorText);
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Add error checking for the response structure
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
        console.error('Unexpected API response structure:', data);
        throw new Error('Unexpected response format from Gemini API');
    }

    return data.candidates[0].content.parts[0].text;
}

// Function to get email context (both received email and current draft)
function getEmailContext() {
    // Try to find the compose area
    const composeArea = document.querySelector('.Am.Al.editable.LW-avf') ||
                       document.querySelector('[contenteditable="true"]') ||
                       document.querySelector('.editable');

    // Try to find the original email content (for replies)
    const originalEmail = document.querySelector('.ii.gt .a3s.aiL') ||
                         document.querySelector('.gmail_quote') ||
                         document.querySelector('.moz-cite-prefix');

    return {
        composeArea,
        currentDraft: composeArea ? composeArea.innerText.trim() : '',
        originalEmail: originalEmail ? originalEmail.innerText.trim() : '',
        hasOriginalEmail: !!originalEmail
    };
}

// Function to generate smart reply based on context
async function generateSmartReply(length = 'medium') {
    try {
        showLoadingState('Analyzing email...');

        const context = getEmailContext();
        const settings = await getApiSettings();

        if (!context.composeArea) {
            alert("Please open a compose window or reply to an email first.");
            resetButtonState();
            return;
        }

        let lengthInstruction;
        switch(length) {
            case 'short':
                lengthInstruction = 'Keep the response brief and concise (1-2 sentences).';
                break;
            case 'long':
                lengthInstruction = 'Provide a detailed and comprehensive response with full explanations.';
                break;
            default:
                lengthInstruction = 'Write a balanced, professional response of moderate length.';
        }

        // Add custom prompt instructions if available
        const customInstructions = settings.customPrompt
            ? `\n\nADDITIONAL CUSTOM INSTRUCTIONS:\n${settings.customPrompt}\n(Follow these custom instructions while maintaining professionalism)`
            : '';

        let prompt;
        if (context.hasOriginalEmail && context.originalEmail) {
            // Replying to an email
            prompt = `Write a direct email reply to the following message. Analyze the tone and style of the original email and match it appropriately.

ORIGINAL EMAIL:
${context.originalEmail}

${context.currentDraft ? `CURRENT DRAFT TO IMPROVE:
${context.currentDraft}

Improve and complete this draft.` : ''}

Instructions:
- Write ONLY the email reply content, no options or alternatives
- Match the tone of the original email (formal/casual/friendly/business)
- Address all key points mentioned in the original email
- ${lengthInstruction}
- Use natural, conversational language
- Do not use asterisks (*) or bullet points
- Write in complete sentences and paragraphs
- Be helpful and professional${customInstructions}`;
        } else {
            // Composing a new email or improving draft
            if (!context.currentDraft || context.currentDraft.length < 10) {
                alert("Please provide some context or draft content first.");
                resetButtonState();
                return;
            }

            prompt = `Improve this email draft. Write the improved version directly without options or alternatives.

CURRENT DRAFT:
${context.currentDraft}

Instructions:
- Write ONLY the improved email content
- Make it more professional and clear
- ${lengthInstruction}
- Use natural, flowing language
- Do not use asterisks (*) or bullet points
- Maintain the original intent and meaning
- Write in complete sentences and paragraphs${customInstructions}`;
        }

        const aiReply = await callGeminiAPI(prompt);

        // Clean up the response - remove asterisks and format properly
        const cleanedReply = aiReply
            .replace(/\*\*/g, '')  // Remove bold asterisks
            .replace(/\*/g, '')    // Remove single asterisks
            .replace(/^\s*[-•]\s*/gm, '') // Remove bullet points
            .trim();

        // Replace the content in the compose area
        context.composeArea.innerHTML = cleanedReply.replace(/\n/g, '<br>');

        // Trigger input event to ensure Gmail recognizes the change
        context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));

    } catch (error) {
        console.error('Smart Reply Error:', error);
        alert("Could not generate smart reply. Please check your API key and try again.");
    } finally {
        resetButtonState();
    }
}

// Function to improve current draft
async function improveDraft(length = 'medium') {
    try {
        showLoadingState('Improving draft...');

        const context = getEmailContext();
        const settings = await getApiSettings();

        if (!context.composeArea || !context.currentDraft || context.currentDraft.length < 10) {
            alert("Please write some content in the email first, then I can help improve it.");
            resetButtonState();
            return;
        }

        let lengthInstruction;
        switch(length) {
            case 'short':
                lengthInstruction = 'Make it more concise and brief while keeping all essential information.';
                break;
            case 'long':
                lengthInstruction = 'Expand the content with more detail, explanations, and context where appropriate.';
                break;
            default:
                lengthInstruction = 'Maintain a balanced length that is neither too brief nor too verbose.';
        }

        // Add custom prompt instructions if available
        const customInstructions = settings.customPrompt
            ? `\n\nADDITIONAL CUSTOM INSTRUCTIONS:\n${settings.customPrompt}\n(Follow these custom instructions while maintaining professionalism)`
            : '';

        const prompt = `Improve this email draft. Write the enhanced version directly without providing options or alternatives.

CURRENT DRAFT:
${context.currentDraft}

Instructions:
- Write ONLY the improved email content
- Make it more professional, clear, and polished
- Fix grammar, style, and structure issues
- ${lengthInstruction}
- Use natural, flowing language
- Do not use asterisks (*) or bullet points unless absolutely necessary
- Maintain the original intent and meaning
- Write in complete sentences and paragraphs
- Ensure proper email tone and etiquette${customInstructions}`;

        const improvedDraft = await callGeminiAPI(prompt);

        // Clean up the response - remove asterisks and format properly
        const cleanedDraft = improvedDraft
            .replace(/\*\*/g, '')  // Remove bold asterisks
            .replace(/\*/g, '')    // Remove single asterisks
            .replace(/^\s*[-•]\s*/gm, '') // Remove bullet points
            .trim();

        // Replace the content
        context.composeArea.innerHTML = cleanedDraft.replace(/\n/g, '<br>');
        context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));

    } catch (error) {
        console.error('Improve Draft Error:', error);
        alert("Could not improve draft. Please check your API key and try again.");
    } finally {
        resetButtonState();
    }
}

// Function to summarize email thread
async function summarizeEmail() {
    try {
        showLoadingState('Summarizing...');

        const context = getEmailContext();

        if (!context.originalEmail) {
            alert("No email content found to summarize. Please open an email thread first.");
            resetButtonState();
            return;
        }

        const prompt = `Create a clean, concise summary of this email without using asterisks, bullet points, or special formatting:

EMAIL CONTENT:
${context.originalEmail}

Write a natural paragraph summary that includes:
- Main topics and key points
- Any action items or requests
- Important dates or deadlines
- Overall purpose and intent

Use complete sentences in paragraph form. Do not use asterisks (*), bullet points, or special formatting symbols.`;

        const summary = await callGeminiAPI(prompt);

        // Clean up the summary - remove asterisks and format properly
        const cleanedSummary = summary
            .replace(/\*\*/g, '')  // Remove bold asterisks
            .replace(/\*/g, '')    // Remove single asterisks
            .replace(/^\s*[-•]\s*/gm, '') // Remove bullet points
            .replace(/^\s*\d+\.\s*/gm, '') // Remove numbered lists
            .trim();

        // Insert summary at the beginning of compose area
        const summaryText = `EMAIL SUMMARY:\n${cleanedSummary}\n\n---\n\n`;

        if (context.composeArea) {
            const currentContent = context.currentDraft;
            context.composeArea.innerHTML = (summaryText + currentContent).replace(/\n/g, '<br>');
            context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));
        } else {
            // Show summary in a popup if no compose area
            alert(`EMAIL SUMMARY:\n\n${cleanedSummary}`);
        }

    } catch (error) {
        console.error('Summarize Error:', error);
        alert("Could not summarize email. Please check your API key and try again.");
    } finally {
        resetButtonState();
    }
}

// Function to show quick response options
async function showQuickResponses() {
    const context = getEmailContext();

    if (!context.composeArea) {
        alert("Please open a compose window first.");
        return;
    }

    // Remove existing quick response popup if any
    const existingPopup = document.querySelector('#quick-responses-popup');
    if (existingPopup) {
        existingPopup.remove();
        return;
    }

    // Show loading popup first
    const loadingPopup = createLoadingPopup();
    document.body.appendChild(loadingPopup);

    try {
        // Generate contextual quick responses based on the email
        const quickResponses = await generateContextualResponses(context);

        // Remove loading popup
        loadingPopup.remove();

        const popup = document.createElement('div');
        popup.id = 'quick-responses-popup';
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border: 1px solid #e2e8f0;
            width: 450px;
            max-height: 500px;
            overflow-y: auto;
            z-index: 10000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            animation: slideDownFadeIn 0.2s ease-out;
        `;

        popup.innerHTML = `
            <div style="padding: 20px; border-bottom: 1px solid #f1f5f9;">
                <h3 style="margin: 0; color: #2d3748; font-size: 18px;">Smart Quick Responses</h3>
                <p style="margin: 8px 0 0 0; color: #718096; font-size: 14px;">AI-generated responses based on your email context</p>
            </div>
            <div style="padding: 12px;">
                ${quickResponses.map((response, index) => `
                    <div class="quick-response-item" style="
                        padding: 12px;
                        margin: 8px 0;
                        border: 1px solid #e2e8f0;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    " data-text="${response.text.replace(/"/g, '&quot;')}" data-index="${index}">
                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 4px;">${response.title}</div>
                        <div style="color: #718096; font-size: 13px; line-height: 1.4;">${response.preview}</div>
                    </div>
                `).join('')}
            </div>
            <div style="padding: 16px; border-top: 1px solid #f1f5f9; display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #718096; font-size: 12px;">✨ Responses tailored to your email</span>
                <button id="close-quick-responses" style="
                    background: #f7fafc;
                    color: #4a5568;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                    padding: 8px 16px;
                    cursor: pointer;
                    font-size: 14px;
                ">Close</button>
            </div>
        `;

        // Add hover effects and click handlers
        popup.addEventListener('click', (e) => {
            const item = e.target.closest('.quick-response-item');
            if (item) {
                const text = item.getAttribute('data-text');
                context.composeArea.innerHTML = text.replace(/\n/g, '<br>');
                context.composeArea.dispatchEvent(new Event('input', { bubbles: true }));
                popup.remove();
            }

            if (e.target.id === 'close-quick-responses') {
                popup.remove();
            }
        });

        // Add hover effects
        popup.addEventListener('mouseover', (e) => {
            const item = e.target.closest('.quick-response-item');
            if (item) {
                item.style.backgroundColor = '#f7fafc';
                item.style.borderColor = '#cbd5e0';
            }
        });

        popup.addEventListener('mouseout', (e) => {
            const item = e.target.closest('.quick-response-item');
            if (item) {
                item.style.backgroundColor = 'white';
                item.style.borderColor = '#e2e8f0';
            }
        });

        document.body.appendChild(popup);

        // Close popup when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closePopup(e) {
                if (!popup.contains(e.target)) {
                    popup.remove();
                    document.removeEventListener('click', closePopup);
                }
            });
        }, 100);

    } catch (error) {
        loadingPopup.remove();
        console.error('Quick Responses Error:', error);
        alert("Could not generate quick responses. Please check your API key and try again.");
    }
}

// Function to create loading popup
function createLoadingPopup() {
    const popup = document.createElement('div');
    popup.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        border: 1px solid #e2e8f0;
        width: 300px;
        padding: 30px;
        text-align: center;
        z-index: 10000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    popup.innerHTML = `
        <div style="color: #4a5568; font-size: 16px; margin-bottom: 10px;">Generating Smart Responses...</div>
        <div style="color: #718096; font-size: 14px;">Analyzing your email context</div>
    `;

    return popup;
}

// Function to generate contextual quick responses
async function generateContextualResponses(context) {
    let prompt;

    if (context.hasOriginalEmail && context.originalEmail) {
        // Generate responses based on the original email
        prompt = `Based on the following email, generate 5 different quick response options. Each response should be appropriate for the context and tone of the original email.

ORIGINAL EMAIL:
${context.originalEmail}

Generate 5 responses with different purposes:
1. A positive/agreeable response
2. A request for more information
3. A scheduling/meeting response
4. A brief acknowledgment
5. A professional thank you

For each response, provide:
- A short title (2-4 words)
- A preview (one sentence description)
- The full response text (1-2 sentences, natural language, no asterisks)

Format as JSON array with objects containing: title, preview, text

Make responses contextual to the email content and match the sender's tone.`;
    } else {
        // Generate general professional responses
        prompt = `Generate 5 professional quick response templates for common email situations:

1. Thank you response
2. Meeting request
3. Information request
4. Positive confirmation
5. Polite follow-up

For each response, provide:
- A short title (2-4 words)
- A preview (one sentence description)
- The full response text (1-2 sentences, natural language, no asterisks)

Format as JSON array with objects containing: title, preview, text

Make responses professional and versatile.`;
    }

    const aiResponse = await callGeminiAPI(prompt);

    try {
        // Try to parse JSON response
        const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const responses = JSON.parse(cleanResponse);

        if (Array.isArray(responses) && responses.length > 0) {
            return responses;
        }
    } catch (error) {
        console.log('JSON parsing failed, using fallback responses');
    }

    // Fallback to default responses if AI response parsing fails
    return [
        {
            title: 'Thank You',
            preview: 'Express gratitude for their email',
            text: 'Thank you for your email. I appreciate you reaching out and will get back to you soon.'
        },
        {
            title: 'More Info',
            preview: 'Request additional details',
            text: 'Thank you for your message. Could you please provide more details so I can better assist you?'
        },
        {
            title: 'Schedule Meeting',
            preview: 'Suggest a meeting or call',
            text: 'Thank you for your email. I would be happy to discuss this further. Could we schedule a meeting?'
        },
        {
            title: 'Acknowledge',
            preview: 'Confirm receipt and review',
            text: 'Thank you for your email. I have received your message and will review it carefully.'
        },
        {
            title: 'Positive Response',
            preview: 'Show interest and agreement',
            text: 'Thank you for your email. This sounds great and I am interested in moving forward.'
        }
    ];
}

// Enhanced observer to add the button when compose windows open
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        // Check for compose windows and reply areas
        if (mutation.addedNodes.length) {
            // Gmail compose toolbar
            const composeToolbars = document.querySelectorAll('.gA.gt');
            composeToolbars.forEach(toolbar => {
                if (!toolbar.querySelector('#ai-reply-button')) {
                    const aiButton = createAiButton();
                    toolbar.appendChild(aiButton);
                }
            });

            // Also check for reply toolbars
            const replyToolbars = document.querySelectorAll('.btC');
            replyToolbars.forEach(toolbar => {
                if (!toolbar.querySelector('#ai-reply-button')) {
                    const aiButton = createAiButton();
                    toolbar.appendChild(aiButton);
                }
            });
        }
    });
});

// Start observing
observer.observe(document.body, { childList: true, subtree: true });

// Also add button to any existing compose windows
setTimeout(() => {
    addAiButton();
}, 1000);