// Function to add the AI Reply button
function addAiButton() {
    const toolbars = document.querySelectorAll('.gA.gt');
    toolbars.forEach(toolbar => {
        if (!toolbar.querySelector('#ai-reply-button')) {
            const aiButton = document.createElement('button');
            aiButton.id = 'ai-reply-button';
            aiButton.innerText = '✨ AI Reply';
            // ... (styling code remains the same)
            aiButton.addEventListener('click', generateAiReply);
            toolbar.appendChild(aiButton);
        }
    });
}

// Function to generate the reply using Gemini API
async function generateAiReply() {
    const button = document.getElementById('ai-reply-button');
    const emailBody = document.querySelector('.Am.Al.editable.LW-avf');

    // First, get the API key from storage
    const storage = await chrome.storage.sync.get('geminiApiKey');
    const apiKey = storage.geminiApiKey;

    if (!apiKey) {
        alert("Gemini API Key not found. Please set it in the extension options.");
        // Optional: Open the options page for the user
        chrome.runtime.sendMessage({ action: "openOptionsPage" });
        return;
    }

    const originalEmailText = emailBody.innerText;
    if (originalEmailText.trim().length < 10) {
        alert("Please provide some context in the email before generating a reply.");
        return;
    }

    button.innerText = '🧠 Generating...';
    button.disabled = true;

    // The official Gemini API endpoint
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`;

    // The data payload for the API
    const payload = {
        contents: [{
            parts: [{
                text: "Based on the following email draft, write a professional and concise reply. Here is the draft:\n\n" + originalEmailText
            }]
        }]
    };

    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`API Error: ${response.statusText}`);
        }

        const data = await response.json();
        // Extract the text from the Gemini response
        const aiReply = data.candidates[0].content.parts[0].text;
        
        emailBody.innerText += `\n\n${aiReply}`;

    } catch (error) {
        console.error('AI Reply Error:', error);
        alert("Could not generate AI reply. Please check your API key and the console for errors.");
    } finally {
        button.innerText = '✨ AI Reply';
        button.disabled = false;
    }
}

// Observer to add the button when the compose window opens
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.addedNodes.length && document.querySelector('.gA.gt')) {
            addAiButton();
        }
    });
});

observer.observe(document.body, { childList: true, subtree: true });