<!DOCTYPE html>
<html>
<head>
    <title>AI Email Assistant Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            color: #718096;
            font-size: 16px;
            line-height: 1.5;
        }

        .api-key-section {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 600;
            font-size: 14px;
        }

        .input-container {
            position: relative;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f7fafc;
        }

        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #fc8181 0%, #f56565 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            display: none;
        }

        .status.success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .status.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .status.info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #63b3ed;
        }

        .key-status {
            margin-top: 20px;
            padding: 16px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .key-status h3 {
            color: #2d3748;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .key-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #718096;
            font-size: 14px;
        }

        .key-preview {
            font-family: 'Courier New', monospace;
            background: #edf2f7;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .icon {
            width: 16px;
            height: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Email Assistant</h1>
            <p>Configure your Google Gemini API key to enable AI-powered email replies</p>
            <div style="margin-top: 15px;">
                <a href="about.html" style="
                    color: #667eea;
                    text-decoration: none;
                    font-weight: 600;
                    margin-right: 20px;
                    font-size: 14px;
                ">📖 How It Works</a>
                <a href="api-instructions.html" style="
                    color: #667eea;
                    text-decoration: none;
                    font-weight: 600;
                    font-size: 14px;
                ">🔑 How to Get API Key</a>
            </div>
        </div>

        <div class="api-key-section">
            <div class="input-group">
                <label for="apiKey">Gemini API Key</label>
                <div style="margin-bottom: 8px;">
                    <a href="api-instructions.html" style="
                        color: #667eea;
                        text-decoration: none;
                        font-size: 14px;
                        font-weight: 500;
                        display: inline-flex;
                        align-items: center;
                        gap: 4px;
                    ">
                        🔑 How to get your free API key
                        <svg style="width: 12px; height: 12px;" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                </div>
                <div class="input-container">
                    <input type="password" id="apiKey" placeholder="Enter your Gemini API key...">
                </div>
            </div>

            <div class="button-group">
                <button id="save" class="btn btn-primary">
                    <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    Save Key
                </button>
                <button id="delete" class="btn btn-danger" style="display: none;">
                    <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    Delete Key
                </button>
            </div>

            <div id="status" class="status"></div>

            <div id="keyStatus" class="key-status" style="display: none;">
                <h3>Current API Key</h3>
                <div class="key-info">
                    <span class="key-preview" id="keyPreview"></span>
                    <span id="keyDate"></span>
                </div>
            </div>
        </div>
    </div>
    <script src="options.js"></script>
</body>
</html>