<!DOCTYPE html>
<html>
<head>
    <title>About - AI Email Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f5f9;
        }

        .header h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            color: #718096;
            font-size: 18px;
            line-height: 1.5;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #2d3748;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h3 {
            color: #4a5568;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            margin-top: 20px;
        }

        .section p, .section li {
            color: #718096;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .section ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .feature-card h4 {
            color: #2d3748;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-card p {
            color: #718096;
            font-size: 14px;
            margin: 0;
        }

        .steps {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .steps ol {
            margin-left: 20px;
        }

        .steps li {
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }

        .highlight {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .highlight p {
            color: #234e52;
            margin: 0;
            font-weight: 500;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .icon {
            font-size: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Email Assistant</h1>
            <p>Your intelligent companion for professional email communication</p>
        </div>

        <div class="section">
            <h2><span class="icon">🚀</span> How It Works</h2>
            <p>The AI Email Assistant integrates seamlessly with Gmail to provide intelligent email assistance powered by Google's Gemini AI. Simply set up your API key once, and the assistant will appear in your Gmail compose and reply windows.</p>
        </div>

        <div class="section">
            <h2><span class="icon">✨</span> Four Powerful Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><span class="icon">📧</span> Smart Reply</h4>
                    <p>Analyzes received emails and generates contextual, professional replies that match the sender's tone and address all key points.</p>
                </div>
                <div class="feature-card">
                    <h4><span class="icon">✍️</span> Improve Draft</h4>
                    <p>Enhances your existing drafts to be more professional, clear, and polished while maintaining your original intent.</p>
                </div>
                <div class="feature-card">
                    <h4><span class="icon">📝</span> Summarize Email</h4>
                    <p>Creates concise summaries of long emails with key points, action items, and important dates.</p>
                </div>
                <div class="feature-card">
                    <h4><span class="icon">🎯</span> Quick Response</h4>
                    <p>Provides AI-generated response templates tailored to your specific email context and situation.</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">📖</span> How to Use</h2>
            
            <h3>Setup (One-time)</h3>
            <div class="steps">
                <ol>
                    <li>Get your free Google Gemini API key</li>
                    <li>Open the extension options page</li>
                    <li>Enter your API key and save</li>
                </ol>
            </div>

            <h3>Using in Gmail</h3>
            <div class="steps">
                <ol>
                    <li>Open Gmail and compose a new email or reply to an existing one</li>
                    <li>Look for the <strong>✨ AI Reply</strong> button in the toolbar</li>
                    <li>Click the button to see four AI options</li>
                    <li>Choose your preferred feature and response length</li>
                    <li>Review the AI-generated content and send!</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">🎨</span> Smart Features</h2>
            <ul>
                <li><strong>Length Control:</strong> Choose short, medium, or detailed responses</li>
                <li><strong>Tone Matching:</strong> Automatically adapts to formal, casual, or business communication styles</li>
                <li><strong>Context Awareness:</strong> Understands whether you're replying or composing new emails</li>
                <li><strong>Clean Formatting:</strong> Generates natural, professional text without unnecessary symbols</li>
                <li><strong>Smart Positioning:</strong> Dropdown menus adapt to your screen position</li>
            </ul>
        </div>

        <div class="section">
            <h2><span class="icon">🔒</span> Privacy & Security</h2>
            <ul>
                <li>Your API key is stored securely in your browser</li>
                <li>Emails are processed by Google's Gemini API</li>
                <li>No data is stored on external servers</li>
                <li>You can delete your API key anytime</li>
                <li>Works entirely within your Gmail interface</li>
            </ul>
        </div>

        <div class="section">
            <h2><span class="icon">💡</span> Pro Tips</h2>
            <ul>
                <li>Combine features: Use Quick Response to start, then Improve Draft to polish</li>
                <li>Provide context: The more information you give, the better the AI responses</li>
                <li>Always review: Check AI-generated content before sending</li>
                <li>Use summaries: Great for understanding long email chains before responding</li>
                <li>Try different lengths: Experiment with short, medium, and detailed responses</li>
            </ul>
        </div>

        <div class="highlight">
            <p><strong>🎯 Result:</strong> Save time, improve your email communication, and maintain professional standards with AI-powered assistance!</p>
        </div>

        <a href="options.html" class="back-button">← Back to Settings</a>
    </div>
</body>
</html>
