<!DOCTYPE html>
<html>
<head>
    <title>How to Get Gemini API Key - AI Email Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f5f9;
        }

        .header h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            color: #718096;
            font-size: 18px;
            line-height: 1.5;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #2d3748;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h3 {
            color: #4a5568;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            margin-top: 20px;
        }

        .section p, .section li {
            color: #718096;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .steps {
            background: #f7fafc;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .steps ol {
            margin-left: 20px;
        }

        .steps li {
            margin-bottom: 15px;
            color: #4a5568;
            font-weight: 500;
            line-height: 1.6;
        }

        .steps li strong {
            color: #2d3748;
        }

        .highlight {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .highlight p {
            color: #234e52;
            margin: 0;
            font-weight: 500;
        }

        .warning {
            background: #fef5e7;
            border: 1px solid #f6ad55;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .warning p {
            color: #744210;
            margin: 0;
            font-weight: 500;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            transform: translateY(-1px);
        }

        .icon {
            font-size: 20px;
        }

        .code {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 3px 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #2d3748;
        }

        .screenshot-placeholder {
            background: #f7fafc;
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            color: #718096;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>How to Get Your Gemini API Key</h1>
            <p>Step-by-step guide to obtain your free Google Gemini API key</p>
        </div>

        <div class="highlight">
            <p><strong>🎉 Good News:</strong> Google Gemini API is free to use with generous limits! Perfect for email assistance.</p>
        </div>

        <div class="section">
            <h2><span class="icon">📋</span> Step-by-Step Instructions</h2>
            
            <div class="steps">
                <ol>
                    <li>
                        <strong>Visit Google AI Studio:</strong><br>
                        Go to <span class="code">aistudio.google.com</span> in your web browser
                    </li>
                    
                    <li>
                        <strong>Sign in with Google:</strong><br>
                        Use your Google account to sign in. If you don't have one, create a free Google account first.
                    </li>
                    
                    <li>
                        <strong>Accept Terms:</strong><br>
                        Review and accept the Google AI Studio terms of service and privacy policy.
                    </li>
                    
                    <li>
                        <strong>Navigate to API Keys:</strong><br>
                        Look for "Get API Key" or "API Keys" in the menu or dashboard. This might be in the left sidebar or top navigation.
                    </li>
                    
                    <li>
                        <strong>Create New API Key:</strong><br>
                        Click "Create API Key" or "Generate API Key" button. You may need to create or select a Google Cloud project.
                    </li>
                    
                    <li>
                        <strong>Choose Project:</strong><br>
                        If prompted, either select an existing Google Cloud project or create a new one. For personal use, the default project is fine.
                    </li>
                    
                    <li>
                        <strong>Copy Your API Key:</strong><br>
                        Once generated, copy the API key immediately. It will look something like: <span class="code">AIzaSyD...</span>
                    </li>
                    
                    <li>
                        <strong>Store Safely:</strong><br>
                        Save your API key in a secure location. You'll need it to configure the AI Email Assistant.
                    </li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">⚡</span> Quick Alternative Method</h2>
            <div class="steps">
                <ol>
                    <li>Go directly to <span class="code">makersuite.google.com/app/apikey</span></li>
                    <li>Sign in with your Google account</li>
                    <li>Click "Create API Key"</li>
                    <li>Copy and save your key</li>
                </ol>
            </div>
        </div>

        <div class="warning">
            <p><strong>⚠️ Important Security Notes:</strong></p>
            <ul style="margin-top: 10px; margin-left: 20px;">
                <li>Never share your API key publicly</li>
                <li>Don't commit API keys to code repositories</li>
                <li>Keep your API key private and secure</li>
                <li>You can regenerate your key if it's compromised</li>
            </ul>
        </div>

        <div class="section">
            <h2><span class="icon">💰</span> Pricing Information</h2>
            <p>Google Gemini API offers generous free usage limits:</p>
            <ul>
                <li><strong>Free Tier:</strong> Includes substantial monthly usage</li>
                <li><strong>Rate Limits:</strong> Sufficient for personal email assistance</li>
                <li><strong>Pay-as-you-go:</strong> Only pay if you exceed free limits</li>
                <li><strong>Cost-effective:</strong> Very affordable for typical usage</li>
            </ul>
        </div>

        <div class="section">
            <h2><span class="icon">🔧</span> Troubleshooting</h2>
            <h3>Can't find API Keys section?</h3>
            <ul>
                <li>Make sure you're signed in to Google AI Studio</li>
                <li>Try refreshing the page</li>
                <li>Look for "Get API Key" or "Credentials" in the menu</li>
            </ul>

            <h3>API Key not working?</h3>
            <ul>
                <li>Ensure you copied the complete key</li>
                <li>Check that the API is enabled for your project</li>
                <li>Wait a few minutes for the key to activate</li>
                <li>Try regenerating the key if issues persist</li>
            </ul>
        </div>

        <div class="highlight">
            <p><strong>✅ Next Step:</strong> Once you have your API key, return to the extension settings and paste it in the "Gemini API Key" field.</p>
        </div>

        <div class="button-group">
            <a href="https://aistudio.google.com" target="_blank" class="btn btn-primary">
                <span class="icon">🚀</span>
                Get API Key Now
            </a>
            <a href="options.html" class="btn btn-secondary">
                <span class="icon">←</span>
                Back to Settings
            </a>
        </div>
    </div>
</body>
</html>
