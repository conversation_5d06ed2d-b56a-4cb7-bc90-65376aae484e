document.addEventListener('DOMContentLoaded', () => {
    const saveButton = document.getElementById('save');
    const apiKeyInput = document.getElementById('apiKey');
    const statusDiv = document.getElementById('status');

    // Load any saved API key and display it in the input
    chrome.storage.sync.get(['geminiApiKey'], (result) => {
        if (result.geminiApiKey) {
            apiKeyInput.value = result.geminiApiKey;
        }
    });

    // Save the key when the button is clicked
    saveButton.addEventListener('click', () => {
        const apiKey = apiKeyInput.value.trim();
        if (apiKey) {
            chrome.storage.sync.set({ geminiApiKey: apiKey }, () => {
                statusDiv.textContent = 'API Key saved successfully!';
                setTimeout(() => { statusDiv.textContent = ''; }, 3000);
            });
        } else {
            statusDiv.textContent = 'Please enter a valid API Key.';
            statusDiv.style.color = 'red';
        }
    });
});