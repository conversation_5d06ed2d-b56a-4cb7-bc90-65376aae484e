document.addEventListener('DOMContentLoaded', () => {
    const saveButton = document.getElementById('save');
    const deleteButton = document.getElementById('delete');
    const apiKeyInput = document.getElementById('apiKey');
    const statusDiv = document.getElementById('status');
    const keyStatusDiv = document.getElementById('keyStatus');
    const keyPreview = document.getElementById('keyPreview');
    const keyDate = document.getElementById('keyDate');

    // Utility functions
    function showStatus(message, type = 'success') {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.style.display = 'block';

        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 4000);
    }

    function updateKeyStatus(hasKey, keyValue = '') {
        if (hasKey) {
            keyStatusDiv.style.display = 'block';
            deleteButton.style.display = 'flex';

            // Show masked version of the key with proper character handling
            let maskedKey;
            if (keyValue && keyValue.length > 8) {
                const firstPart = keyValue.substring(0, 4);
                const lastPart = keyValue.substring(keyValue.length - 4);
                maskedKey = firstPart + '••••••••' + lastPart;
            } else if (keyValue && keyValue.length > 0) {
                maskedKey = '••••••••';
            } else {
                maskedKey = 'No key saved';
            }
            keyPreview.textContent = maskedKey;

            // Show when it was saved (we'll store this info)
            const savedDate = localStorage.getItem('geminiApiKeySavedDate');
            if (savedDate) {
                try {
                    const date = new Date(savedDate);
                    if (!isNaN(date.getTime())) {
                        keyDate.textContent = `Saved: ${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
                    } else {
                        keyDate.textContent = 'Saved: Recently';
                    }
                } catch (error) {
                    keyDate.textContent = 'Saved: Recently';
                }
            } else {
                keyDate.textContent = 'Saved: Recently';
            }
        } else {
            keyStatusDiv.style.display = 'none';
            deleteButton.style.display = 'none';
        }
    }

    function validateApiKey(key) {
        // Basic validation for Gemini API key format
        if (!key || typeof key !== 'string') {
            return { valid: false, message: 'Please enter a valid API key' };
        }

        // Trim whitespace and check length
        const trimmedKey = key.trim();
        if (trimmedKey.length < 10) {
            return { valid: false, message: 'API key is too short (minimum 10 characters)' };
        }

        // Check for valid characters (alphanumeric, underscore, hyphen)
        if (!/^[A-Za-z0-9_-]+$/.test(trimmedKey)) {
            return { valid: false, message: 'API key should only contain letters, numbers, underscores, and hyphens' };
        }

        return { valid: true };
    }

    // Load any saved API key and display it
    chrome.storage.sync.get(['geminiApiKey'], (result) => {
        if (result.geminiApiKey) {
            apiKeyInput.value = result.geminiApiKey;
            updateKeyStatus(true, result.geminiApiKey);
        } else {
            updateKeyStatus(false);
        }
    });

    // Save the key when the button is clicked
    saveButton.addEventListener('click', () => {
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            showStatus('Please enter an API key', 'error');
            return;
        }

        const validation = validateApiKey(apiKey);
        if (!validation.valid) {
            showStatus(validation.message, 'error');
            return;
        }

        // Disable button during save
        saveButton.disabled = true;
        saveButton.innerHTML = `
            <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
            </svg>
            Saving...
        `;

        chrome.storage.sync.set({ geminiApiKey: apiKey }, () => {
            // Store the save date in local storage
            localStorage.setItem('geminiApiKeySavedDate', new Date().toISOString());

            showStatus('API Key saved successfully! 🎉', 'success');
            updateKeyStatus(true, apiKey);

            // Reset button
            saveButton.disabled = false;
            saveButton.innerHTML = `
                <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                Save Key
            `;
        });
    });

    // Delete the key when the delete button is clicked
    deleteButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to delete the saved API key? This action cannot be undone.')) {
            // Disable button during delete
            deleteButton.disabled = true;
            deleteButton.innerHTML = `
                <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                </svg>
                Deleting...
            `;

            chrome.storage.sync.remove(['geminiApiKey'], () => {
                // Remove the save date from local storage
                localStorage.removeItem('geminiApiKeySavedDate');

                // Clear the input field
                apiKeyInput.value = '';

                showStatus('API Key deleted successfully', 'info');
                updateKeyStatus(false);

                // Reset button
                deleteButton.disabled = false;
                deleteButton.innerHTML = `
                    <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    Delete Key
                `;
            });
        }
    });

    // Add input validation feedback
    apiKeyInput.addEventListener('input', () => {
        const apiKey = apiKeyInput.value.trim();
        if (apiKey) {
            const validation = validateApiKey(apiKey);
            if (!validation.valid) {
                apiKeyInput.style.borderColor = '#fc8181';
            } else {
                apiKeyInput.style.borderColor = '#48bb78';
            }
        } else {
            apiKeyInput.style.borderColor = '#e2e8f0';
        }
    });

    // Handle Enter key press in input field
    apiKeyInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            saveButton.click();
        }
    });
});